import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import Showcase from "@/components/blocks/showcase";
import StructuredData from "@/components/structured-data";
import CircleCrop from "@/components/circle-crop";
import { getLandingPage } from "@/services/page";
import { setRequestLocale, getTranslations } from "next-intl/server";

export const revalidate = 60;
export const dynamic = "force-static";
export const dynamicParams = true;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  const page = await getLandingPage(locale);
  const t = await getTranslations();

  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || "";
  const canonicalUrl = locale === "en" ? baseUrl : `${baseUrl}/${locale}`;

  // 从页面数据获取标题和描述
  const title = t("metadata.title");
  const description = t("metadata.description");

  // Open Graph 图片
  const ogImage = `${baseUrl}/imgs/screenshot.jpg`;

  return {
    title,
    description,
    keywords: "",

    // Open Graph
    openGraph: {
      title,
      description,
      url: canonicalUrl,
      siteName: "Crop Image",
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: locale === "zh" ? "zh_CN" : "en_US",
      type: "website",
    },

    // 其他元数据
    alternates: {
      canonical: canonicalUrl,
    }
  };
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  setRequestLocale(locale);

  const page = await getLandingPage(locale);
  const t = await getTranslations();

  // 获取圆形裁剪页面的数据
  const circleCropPage = await import(`@/i18n/pages/circle-crop-image/${locale}.json`);

  // 构建当前页面URL
  const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || "";
  const currentUrl = locale === "en" ? baseUrl : `${baseUrl}/${locale}`;

  return (
    <>
      {/* 结构化数据 */}
      <StructuredData page={page} locale={locale} url={currentUrl} />

      {page.hero && <Hero hero={page.hero} />}

      {/* 圆形裁剪工具 */}
      <section id="circle-crop-tool" className="py-24">
        <div className="container">
          <CircleCrop
            texts={circleCropPage.default.circleCrop}
          />
        </div>
      </section>

      {page.usage && <Feature3 section={page.usage} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
    </>
  );
}
